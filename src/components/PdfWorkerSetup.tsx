'use client';

import { useEffect } from 'react';
import { pdfjs } from 'react-pdf';

// This component sets up the PDF.js worker for react-pdf on the client side.
// It assumes that 'pdf.worker.min.js' has been copied to the '/public' directory.

export default function PdfWorkerSetup() {
  useEffect(() => {
    // Path to the worker file in the public directory.
    // Ensure pdf.worker.min.js from 'node_modules/pdfjs-dist/build/' (or legacy build if needed)
    // is copied to your 'public' folder.
    const workerSrc = '/pdf.worker.min.js';

    // Check if the worker source is already set to avoid re-setting it unnecessarily,
    // though react-pdf might also handle this internally.
    if (pdfjs.GlobalWorkerOptions.workerSrc !== workerSrc) {
      pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;
      console.log('Frontend PDF.js worker source successfully set to:', workerSrc);
    }
  }, []); // Runs once on component mount

  return null; // This component does not render any UI.
}