
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON><PERSON>', sans-serif;
}

@layer base {
  :root {
    /* Light Mode - Radix Slate */
    --background: 240 20% 98%; /* slate-2 */
    --foreground: 210 12.5% 12.5%; /* slate-12 */
    --muted: 240 11.1% 94.7%; /* slate-3 */
    --muted-foreground: 220 5.9% 40%; /* slate-11 */
    --popover: 240 20% 99%; /* slate-1 */
    --popover-foreground: 210 12.5% 12.5%; /* slate-12 */
    --card: 240 20% 99%; /* slate-1 */
    --card-foreground: 210 12.5% 12.5%; /* slate-12 */
    --border: 240 10.1% 86.5%; /* slate-6 */
    --input: 233 9.9% 82.2%; /* slate-7 */
    --primary: 231 5.9% 57.1%; /* slate-9 */
    --primary-foreground: 240 20% 99%; /* slate-1 - Adjusted for contrast */
    --secondary: 240 11.1% 94.7%; /* slate-3 */
    --secondary-foreground: 231 5.9% 57.1%; /* slate-9 */
    --accent: 240 10.1% 86.5%; /* slate-6 */
    --accent-foreground: 231 5.9% 57.1%; /* slate-9 */
    --destructive: 0 84.2% 60.2%; /* Keeping original red */
    --destructive-foreground: 0 0% 98%; /* Keeping original red */
    --ring: 231 5.9% 57.1%; /* slate-9 */
    --radius: 0.5rem;

    /* Sidebar specific (can be adjusted further if needed) */
    --sidebar-background: 240 20% 98%; /* slate-2 */
    --sidebar-foreground: 220 5.9% 40%; /* slate-11 */
    --sidebar-primary: 231 5.9% 57.1%; /* slate-9 */
    --sidebar-primary-foreground: 240 20% 99%; /* slate-1 */
    --sidebar-accent: 240 9.5% 91.8%; /* slate-4 */
    --sidebar-accent-foreground: 231 5.9% 57.1%; /* slate-9 */
    --sidebar-border: 240 10.1% 86.5%; /* slate-6 */
    --sidebar-ring: 231 5.9% 57.1%; /* slate-9 */
  }

  .dark {
    /* Dark Mode - Radix Slate */
    --background: 240 5.6% 7.1%; /* slate-1 */
    --foreground: 220 9.1% 93.5%; /* slate-12 */
    --muted: 225 5.7% 13.7%; /* slate-3 */
    --muted-foreground: 216 6.8% 71%; /* slate-11 */
    --popover: 220 5.9% 10%; /* slate-2 */
    --popover-foreground: 220 9.1% 93.5%; /* slate-12 */
    --card: 220 5.9% 10%; /* slate-2 */
    --card-foreground: 220 9.1% 93.5%; /* slate-12 */
    --border: 213 7.7% 22.9%; /* slate-6 */
    --input: 213 7.6% 28.4%; /* slate-7 */
    --primary: 219 6.3% 43.9%; /* slate-9 */
    --primary-foreground: 240 5.6% 7.1%; /* slate-1 - Adjusted for contrast */
    --secondary: 225 5.7% 13.7%; /* slate-3 */
    --secondary-foreground: 220 9.1% 93.5%; /* slate-12 */
    --accent: 213 7.7% 22.9%; /* slate-6 */
    --accent-foreground: 220 9.1% 93.5%; /* slate-12 */
    --destructive: 0 70% 50%; /* Keeping original red */
    --destructive-foreground: 0 0% 98%; /* Keeping original red */
    --ring: 219 6.3% 43.9%; /* slate-9 */

    /* Sidebar specific (dark) */
    --sidebar-background: 240 5.6% 7.1%; /* slate-1 */
    --sidebar-foreground: 216 6.8% 71%; /* slate-11 */
    --sidebar-primary: 219 6.3% 43.9%; /* slate-9 */
    --sidebar-primary-foreground: 240 5.6% 7.1%; /* slate-1 */
    --sidebar-accent: 210 7.1% 16.5%; /* slate-4 */
    --sidebar-accent-foreground: 220 9.1% 93.5%; /* slate-12 */
    --sidebar-border: 213 7.7% 22.9%; /* slate-6 */
    --sidebar-ring: 219 6.3% 43.9%; /* slate-9 */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Apply background gradient */
     /* background: var(--background); Ensure solid color is applied */
     background-color: hsl(var(--background));
  }

  /* Gradient classes removed */

}

/* Custom styles for markdown content */
@layer components {
  /* Improved markdown styling for better readability */
  .markdown-content {
    /* Typography */
    @apply text-base leading-7;
    
    /* Headings */
    & h1 {
      @apply text-2xl font-bold mt-6 mb-4;
    }
    & h2 {
      @apply text-xl font-bold mt-5 mb-3;
    }
    & h3 {
      @apply text-lg font-semibold mt-4 mb-2;
    }
    & h4, & h5, & h6 {
      @apply font-semibold mt-3 mb-2;
    }
    
    /* Lists */
    & ul, & ol {
      @apply pl-6 my-3 space-y-1;
    }
    & ul {
      @apply list-disc;
    }
    & ol {
      @apply list-decimal;
    }
    
    /* Code blocks */
    & pre {
      @apply bg-muted text-foreground p-3 rounded-md my-4 overflow-x-auto;
    }
    & code {
      @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono;
    }
    & pre code {
      @apply bg-transparent p-0 text-sm;
    }
    
    /* Tables */
    & table {
      @apply w-full my-4 border-collapse;
    }
    & th, & td {
      @apply border border-border p-2;
    }
    & th {
      @apply bg-muted font-semibold;
    }
    
    /* Block elements */
    & p {
      @apply my-3;
    }
    & blockquote {
      @apply border-l-4 border-muted-foreground pl-4 py-1 my-3 italic;
    }
    & hr {
      @apply my-6 border-border;
    }
    
    /* Spacing between sections */
    & > * + :is(h1, h2, h3, h4, h5, h6) {
      @apply mt-6;
    }
  }
}
