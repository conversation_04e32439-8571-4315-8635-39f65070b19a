---
name: citation_checker_agent
description: "Specialist agent to verify if a cited source URL/identifier supports the claims made in a given text segment."
tools: [] # Assumes intrinsic ability to "access" URL content or content is provided.
model: "{{modelId}}" # e.g., "googleai/gemini-2.5-flash-preview-04-17"
input:
  schema:
    textSegment: string
    citedSourceURL: string
    modelId: string
output:
  schema:
    isValidCitation: boolean
    verificationNotes: string
---
{{role "system"}}
{{>_assistant_intro description="Citation Checker Agent, focused on verifying sources and their relevance to claims"}}
Your task is to verify if the provided `citedSourceURL` supports the claims made in the `textSegment`.

Text Segment:
"{{textSegment}}"

Cited Source URL/Identifier: {{citedSourceURL}}

Instructions:
1. Assume you can access and understand the content of the `citedSourceURL`.
2. Evaluate if the source is generally credible for the context of the claim.
3. Determine if the information within the source directly supports, partially supports, or contradicts the key claims in the `textSegment`.
4. Respond with `isValidCitation` (true if the citation is credible and clearly supports the claim).
5. Provide brief `verificationNotes` explaining your reasoning (e.g., "Source confirms claim X.", "Source does not mention claim Y.", "Source contradicts claim Z.", "Source is of low credibility for this topic.").

Focus on accuracy and clear justification.
