// This file contains data shared between client and server components.
// List of available Gemini models for the frontend dropdown
export const availableGeminiModels = [
  {
    id: "googleai/gemini-2.5-flash-preview-04-17",
    name: "Gemini 2.5 Flash",
  },
  {
    id: "googleai/gemini-2.5-pro-preview-03-25",
    name: "Gemini 2.5 Pro",
  },
];

// List of available OpenAI models for the frontend dropdown
export const availableOpenAIModels = [
  { id: "openai/o4-mini", name: "o4 mini" },
  { id: "openai/gpt-4.1", name: "GPT-4.1" },
  { id: "openai/gpt-4.1-mini", name: "GPT-4.1 mini" },
  { id: "openai/gpt-4.1-nano", name: "GPT-4.1 nano" },
  { id: "openai/o3", name: "o3" },
  // Add other desired OpenAI models here
];
