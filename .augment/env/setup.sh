#!/bin/bash
set -e

echo "Setting up Genkit Multi-LLM Chat Studio development environment..."

# Update system packages
sudo apt-get update

# Install Node.js 18 (LTS) if not already installed
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'.' -f1 | cut -d'v' -f2) -lt 18 ]]; then
    echo "Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verify Node.js version
echo "Node.js version: $(node -v)"
echo "npm version: $(npm -v)"

# Install project dependencies
echo "Installing project dependencies..."
npm install

# Create required directories
mkdir -p uploads
mkdir -p .genkit
mkdir -p .db
mkdir -p .genkit_sessions

# Create a minimal .env.local file if it doesn't exist (for build validation)
if [ ! -f .env.local ]; then
    echo "Creating minimal .env.local file for build validation..."
    cat > .env.local << 'EOF'
# Minimal environment variables for build validation
# Replace with actual API keys for full functionality
GOOGLE_API_KEY=AIzaSyBauI7fvjydmwhQqFyARC1F-DwMpi6pQ3U
OPENAI_API_KEY=********************************************************************************************************************************************************************
TAVILY_API_KEY=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
PERPLEXITY_API_KEY=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
GCLOUD_PROJECT=mlops-dev-330107
ANTHROPIC_API_KEY=************************************************************************************************************
EOF
fi

# Run TypeScript type checking
echo "Running TypeScript type checking..."
npm run typecheck

# Run ESLint
echo "Running ESLint..."
npm run lint

# Test Next.js build process
echo "Testing Next.js build process..."
npm run build

echo "Setup completed successfully!"
echo "To start the development server, run: npm run dev"
echo "The application will be available at http://localhost:9002"