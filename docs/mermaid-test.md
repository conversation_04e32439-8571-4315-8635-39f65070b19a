# Mermaid Diagram Test Documentation

This document contains various Mermaid diagram examples to test the rendering functionality in Genkit Studio.

## Test Cases

### 1. Simple Flowchart

```mermaid
flowchart TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    C --> D[Rethink]
    D --> B
    B ---->|No| E[End]
```

### 2. RAG (Retrieval-Augmented Generation) System

```mermaid
flowchart TD
    A[User Query] --> B[Agent]
    B --> C{Information Retrieval Needed?}
    C -->|Yes| D[Retriever]
    D --> E[Vector Database]
    E --> F[Relevant Documents]
    F --> G[LLM with Context]
    C -->|No| G[LLM Direct]
    G --> H[Response Generation]
    H --> I[User Response]
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style G fill:#fff3e0
    style E fill:#f3e5f5
```

### 3. Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Database
    
    User->>Frontend: Submit Query
    Frontend->>API: POST /api/chat
    API->>Database: Retrieve Context
    Database-->>API: Return Data
    API->>API: Process with LLM
    API-->>Frontend: Stream Response
    Frontend-->>User: Display Result
```

### 4. Class Diagram

```mermaid
classDiagram
    class ChatMessage {
        +String id
        +String sender
        +String text
        +Array toolInvocations
        +Array sources
        +render()
    }
    
    class MermaidDiagram {
        +String chart
        +String id
        +Boolean rendered
        +validate()
        +render()
    }
    
    ChatMessage ||--o{ MermaidDiagram : contains
```

### 5. State Diagram

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Loading : user_input
    Loading --> Streaming : response_start
    Streaming --> Rendered : complete
    Rendered --> Idle : new_query
    Loading --> Error : timeout
    Error --> Idle : retry
```

### 6. Gantt Chart

```mermaid
gantt
    title Genkit Studio Development
    dateFormat  YYYY-MM-DD
    section Planning
    Requirements       :done,    req, 2024-01-01, 2024-01-15
    Design            :done,    design, 2024-01-10, 2024-01-25
    section Development
    Core Features     :active,  dev, 2024-01-20, 2024-02-15
    Mermaid Support   :done,    mermaid, 2024-02-01, 2024-02-10
    Testing          :         test, 2024-02-10, 2024-02-20
```

### 7. Pie Chart

```mermaid
pie title Chat Message Types
    "User Messages" : 45
    "Bot Responses" : 40
    "System Messages" : 10
    "Error Messages" : 5
```

### 8. Git Graph

```mermaid
gitgraph
    commit id: "Initial"
    branch feature
    checkout feature
    commit id: "Add Mermaid"
    commit id: "Fix Streaming"
    checkout main
    commit id: "Bug fixes"
    merge feature
    commit id: "Release"
```

### 9. Entity Relationship Diagram

```mermaid
erDiagram
    CHAT-SESSION ||--o{ MESSAGE : contains
    MESSAGE ||--o{ TOOL-INVOCATION : has
    MESSAGE ||--o{ SOURCE : references
    
    CHAT-SESSION {
        string id PK
        timestamp created
        string user_id
    }
    
    MESSAGE {
        string id PK
        string session_id FK
        string sender
        text content
        timestamp created
    }
    
    TOOL-INVOCATION {
        string id PK
        string message_id FK
        string tool_name
        json input
        json output
    }
```

### 10. Journey Map

```mermaid
journey
    title User Chat Experience
    section Discovery
      Open App        : 5: User
      See Interface   : 4: User
    section Interaction
      Type Query      : 3: User
      Wait Response   : 2: User, System
      Read Answer     : 5: User
    section Advanced
      Upload File     : 4: User
      RAG Query       : 5: User, System
      Export Results  : 4: User
```

## Testing Instructions

1. **Load Test**: Open this document in Genkit Studio
2. **Rendering Test**: Verify all diagrams render correctly
3. **Theme Test**: Switch between light/dark themes
4. **Streaming Test**: Generate responses containing Mermaid diagrams
5. **Error Test**: Try invalid syntax to verify error handling
6. **Interaction Test**: Use zoom, copy, and source code features

## Expected Behavior

- ✅ All diagrams should render without flickering
- ✅ No hydration errors in console
- ✅ Smooth streaming without interference
- ✅ Proper error messages for invalid syntax
- ✅ Theme-aware rendering (dark/light mode support)
- ✅ Responsive design across screen sizes

## Common Issues to Watch For

- **"Preparing diagram..." stuck**: Check for incomplete syntax during streaming
- **Blank diagrams**: Verify Mermaid library loading
- **Flickering**: Should be resolved with debounced rendering
- **Hydration errors**: Should be prevented with client-side only rendering
- **Theme mismatch**: Check theme detection logic

## Debugging

If diagrams fail to render:

1. Check browser console for detailed error messages
2. Click "View source code" to verify diagram syntax
3. Try the "History Tokens" button to check memory usage
4. Verify the diagram type is supported
5. Check for network issues preventing Mermaid library loading

## Performance Notes

- Diagrams use 500ms debounce to prevent streaming interference
- Large diagrams may take longer to render
- Token usage includes Mermaid content in conversation history
- Memory cleanup prevents accumulation of failed render attempts