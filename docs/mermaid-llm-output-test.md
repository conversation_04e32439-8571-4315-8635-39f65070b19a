# Mermaid LLM Output Test Cases

This document contains test cases for the specific LLM output formats that need to be detected and converted to proper Mermaid diagrams.

## Test Case 1: Original Problematic Format

This is the exact format that was failing to render:

Here's a Mermaid diagram representing an agentic Retrieval-Augmented Generation (RAG) pipeline. The agentic aspect means the system uses autonomous agents (components that reason and act) to refine queries, select tools, and synthesize answers.Below is the Mermaid syntax for a flowchart that illustrates this architecture:mermaidflowchart TD User([User Question]) Agent(Agent/Orchestrator) Retriever(Retriever) Docs[External Data Sources] LLM(Generative LLM) Synthesizer(Synthesizer/Agent) Answer([Final Answer]) User -->|1. Input| Agent Agent -->|2. Decompose/Plan| Agent Agent -->|3. Generate Query| Retriever Retriever -->|4. Search| Docs Docs -->|5. Relevant Docs| Retriever Retriever -->|6. Return Passages| Agent Agent -->|7. Context Assembly| LLM LLM -->|8. Draft Answer| Synthesizer Synthesizer -->|9. Reason & Refine| Agent Agent -->|10. Output| AnswerYou can copy and paste this code into a Mermaid live editor (like mermaid.live) to visualize the chart.Let me know if you want a different diagram style (e.g., sequence diagram) or further details about each component!

## Test Case 2: Alternative Format 1

Here's another common format:

Below is a mermaid diagram showing the data flow:

mermaid
graph LR
    A[Input] --> B[Process]
    B --> C[Output]

## Test Case 3: Alternative Format 2

Sometimes LLMs output it like this:

```mermaid
sequenceDiagram
    participant User
    participant API
    User->>API: Request
    API-->>User: Response
```

## Test Case 4: Inline Mermaid

Direct inline format: mermaidpie title Breakdown "Section A" : 386 "Section B" : 85 "Section C" : 15

## Test Case 5: Multiple Diagrams

First diagram:
mermaidflowchart TD
    A --> B
    B --> C

Second diagram:
mermaidsequenceDiagram
    Alice->>Bob: Hello
    Bob-->>Alice: Hi

## Expected Behavior

The chat message processor should:

1. **Detect** raw Mermaid content even without proper code block formatting
2. **Convert** `mermaidflowchart` to `flowchart` (remove leading "mermaid" word)
3. **Wrap** the content in proper triple backticks with `mermaid` language tag
4. **Preserve** the rest of the message content
5. **Handle** multiple diagrams in one message
6. **Maintain** proper spacing and formatting

## Conversion Examples

### Before (Raw LLM Output)
```
Here's a diagram:mermaidflowchart TD A --> B
Let me know if you need changes!
```

### After (Processed)
```
Here's a diagram:

```mermaid
flowchart TD
    A --> B
```

Let me know if you need changes!
```

## Testing Instructions

1. Copy any of the "Test Case" examples above
2. Paste them as a bot message in the chat
3. Verify that Mermaid diagrams render correctly
4. Check that surrounding text is preserved
5. Ensure no extra spaces or formatting issues

## Common Issues to Watch For

- **Missing spaces**: `mermaidflowchart` should become `flowchart`
- **Improper wrapping**: Content should be in proper code blocks
- **Lost context**: Surrounding explanatory text should be preserved
- **Multiple diagrams**: Each should be wrapped separately
- **Syntax preservation**: Original Mermaid syntax should remain intact

## Debug Information

If diagrams still don't render:

1. Check browser console for conversion logs
2. Use "View source code" to see if conversion happened
3. Verify the Mermaid syntax is valid
4. Check for JSON parsing errors in SSE events
5. Look for hydration issues in component rendering